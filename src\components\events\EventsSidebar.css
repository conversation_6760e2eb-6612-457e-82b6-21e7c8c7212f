.sidebar {
  width: 250px;
  background-color: #181818;
  color: #fff;
  padding: 20px 0;
  border-right: 1px solid #333;
  height: 100%;
}

.sidebar-section {
  margin-bottom: 10px;
}

.sidebar-btn {
  width: 100%;
  padding: 12px 24px;
  background: none;
  border: none;
  color: #fff;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
}

.sidebar-btn:hover {
  background-color: transparent;
}

.sidebar-dropdown {
  background-color: #1a1a1a;
  padding: 5px 0;
}

.sidebar-btn-sub {
  padding: 10px 20px;
  font-size: 13px;
  color: #ccc;
}

.sidebar-btn-sub:hover {
  background-color: transparent;
  color: #fff;
}

.events-sidebar-item {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.2s;
}

.events-sidebar-item.active,
.events-sidebar-item:hover {
  background: transparent;
  font-weight: bold;
}

.events-sidebar-icon {
  margin-right: 12px;
  font-size: 14px;
}

.sidebar-icon {
  margin-right: 12px;
  font-size: 14px !important;
  width: 14px !important;
  height: 14px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 14px;
}

/* Override ModernSidebar.css styles for consistent icon sizing */
.sidebar-btn .sidebar-icon {
  width: 14px !important;
  height: 14px !important;
  font-size: 14px !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  margin-right: 12px !important;
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
}

/* Ensure Material UI SVG icons are properly sized */
.sidebar-btn .sidebar-icon svg {
  width: 14px !important;
  height: 14px !important;
  font-size: 14px !important;
}